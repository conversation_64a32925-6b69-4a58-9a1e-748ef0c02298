import React from 'react';
import Drawer from '@mui/material/Drawer';
import IconButton from '@mui/material/IconButton';
import Box from '@mui/material/Box';
import Divider from '@mui/material/Divider';
import Brightness4Icon from '@mui/icons-material/Brightness4';
import Brightness7Icon from '@mui/icons-material/Brightness7';
import HomeIcon from '@mui/icons-material/Home';
import HistoryIcon from '@mui/icons-material/History';
import SettingsIcon from '@mui/icons-material/Settings';
import AssignmentIcon from '@mui/icons-material/Assignment';

const navItems = [
  { text: 'Úvod', icon: <HomeIcon />, section: 'intro' },
  { text: 'Odstávky', icon: <AssignmentIcon />, section: 'outages' },
  { text: 'Checklist', icon: <AssignmentIcon />, section: 'checklist' },
  { text: 'Historie', icon: <HistoryIcon />, section: 'history' },
  { text: 'Nastavení', icon: <SettingsIcon />, section: 'settings' },
];

function Sidebar({ section, handleNav, mode, setMode }) {
  return (
    <Drawer
      variant="permanent"
      sx={{
        width: 80,
        flexShrink: 0,
        [`& .MuiDrawer-paper`]: {
          width: 80,
          boxSizing: 'border-box',
          background: '#004C97',
          color: '#fff',
          borderRight: 0,
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          pt: 2,
        },
      }}
      PaperProps={{ elevation: 3 }}
      open
    >
      <Divider sx={{ my: 2, background: 'rgba(255,255,255,0.2)' }} />
      {navItems.map((item) => (
        <IconButton
          key={item.section}
          color={section === item.section ? 'secondary' : 'inherit'}
          onClick={() => handleNav(item.section)}
          sx={{ mb: 2, fontSize: 32, background: section === item.section ? '#e3f6f9' : 'transparent', borderRadius: 2, transition: 'background 0.2s', '&:hover': { background: '#1565c0' } }}
          aria-label={item.text}
        >
          {item.icon}
        </IconButton>
      ))}
      <Box sx={{ flexGrow: 1 }} />
      <IconButton sx={{ mb: 2 }} onClick={() => setMode(mode === 'light' ? 'dark' : 'light')} color="inherit" aria-label="Přepnout režim">
        {mode === 'dark' ? <Brightness7Icon /> : <Brightness4Icon />}
      </IconButton>
    </Drawer>
  );
}

export default Sidebar; 