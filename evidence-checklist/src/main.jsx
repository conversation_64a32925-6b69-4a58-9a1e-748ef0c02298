import React from 'react';
import ReactDOM from 'react-dom/client';
import App from './App.jsx';
import './index.css';
import { ThemeProvider, createTheme, CssBaseline } from '@mui/material';
import '@fontsource/carlito';

const theme = createTheme({
  palette: {
    primary: {
      main: '#004C97', // Tmavě modrá OTE
    },
    secondary: {
      main: '#00A9CE', // Světle modrá OTE
    },
    success: {
      main: '#64A70B', // Zelená OTE
    },
    error: {
      main: '#DF4661', // Růžovo červená OTE
    },
    warning: {
      main: '#FFBF3F', // Žlutá OTE
    },
    info: {
      main: '#B76CA4', // Fialová OTE
    },
    text: {
      primary: '#004C97',
      secondary: '#616265',
    },
    background: {
      default: '#f7fafd',
    },
  },
  typography: {
    fontFamily: '<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, sans-serif',
    h1: { fontWeight: 700 },
    h2: { fontWeight: 700 },
    h3: { fontWeight: 700 },
    h4: { fontWeight: 700 },
    h5: { fontWeight: 700 },
    h6: { fontWeight: 700 },
    button: { textTransform: 'none', fontWeight: 600 },
  },
});

ReactDOM.createRoot(document.getElementById('root')).render(
  <React.StrictMode>
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <App />
    </ThemeProvider>
  </React.StrictMode>
);
