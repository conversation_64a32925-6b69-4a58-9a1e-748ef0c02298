const checklistData = [
  { id: 1, title: 'Kontrola přihlašovací stránky OTE market', detail: 'ote-cr.cz – že již není opona' },
  { id: 2, title: 'Interní test portálu', detail: 'todo' },
  { id: 3, title: 'Test podpisových certifikátů', detail: 'založení rekla<PERSON>, DT, OTE-COM' },
  { id: 4, title: 'Kontrola DT + zkontrolovat pomocí AMQP', detail: 'správa mailové fronty' },
  { id: 5, title: 'OTE-COM', detail: 'elektřina, plyn' },
  { id: 6, title: 'Mobilní aplikace', detail: 'POZE, VDT, VDP' },
  { id: 7, title: 'SAP', detail: 'CDP, KSM, KSP, OZP' },
  { id: 8, title: 'Zavolat na N4T4GAS a informovat je, že je konec odstávky a mohou povolit komunikaci směrem k OTE', detail: 'pí. Lerbleiterová tel. 604 222 872 – nominace' },
  { id: 9, title: 'KSP – kontrola přijatých nominací', detail: '' },
  { id: 10, title: 'PMB – kontrola statusů', detail: '' },
  { id: 11, title: 'Nastavit vývěsku v portálu o ukončení odstávky', detail: '' },
  { id: 12, title: 'Nastavit korektní informace na stránce k aktuálnímu stavu odstávky', detail: 'Informace k aktuální odstávce systému' },
  { id: 13, title: 'Poslat testovací zprávu 621', detail: 'žádost o agregovaná <NAME_EMAIL> zabezpečeným emailem' },
  { id: 14, title: 'Ověření funkčnosti EZP portálu', detail: 'todo: zjistit, zda existuje jiná možnost testu než ověření synchronizace' },
  { id: 15, title: 'Poslat e-mail na OTE všichni', detail: 'o ukončení odstávky systému' },
];

export default checklistData; 