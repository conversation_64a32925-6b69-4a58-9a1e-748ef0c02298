import { useState, useMemo, createContext, useContext } from 'react'
import './App.css'
import OutageList from './OutageList'
import Checklist from './Checklist'
import Dashboard from './Dashboard'
import AppBar from '@mui/material/AppBar'
import Toolbar from '@mui/material/Toolbar'
import Typography from '@mui/material/Typography'
import IconButton from '@mui/material/IconButton'
import MenuIcon from '@mui/icons-material/Menu'
import Drawer from '@mui/material/Drawer'
import List from '@mui/material/List'
import ListItem from '@mui/material/ListItem'
import ListItemButton from '@mui/material/ListItemButton'
import ListItemIcon from '@mui/material/ListItemIcon'
import ListItemText from '@mui/material/ListItemText'
import HomeIcon from '@mui/icons-material/Home'
import HistoryIcon from '@mui/icons-material/History'
import SettingsIcon from '@mui/icons-material/Settings'
import AssignmentIcon from '@mui/icons-material/Assignment'
import Box from '@mui/material/Box'
import Divider from '@mui/material/Divider'
import Snackbar from '@mui/material/Snackbar'
import MuiAlert from '@mui/material/Alert'
import Brightness4Icon from '@mui/icons-material/Brightness4'
import Brightness7Icon from '@mui/icons-material/Brightness7'
import { ThemeProvider, createTheme, CssBaseline } from '@mui/material'
import WelcomeCard from './WelcomeCard'

// Kontext pro notifikace
const SnackbarContext = createContext(null)
export const useSnackbar = () => useContext(SnackbarContext)

// Logo OTE (SVG inline pro rychlé použití)
const OTELogo = () => (
  <svg width="60" height="32" viewBox="0 0 180 96" fill="none" xmlns="http://www.w3.org/2000/svg" style={{ marginRight: 16 }}>
    <text x="0" y="72" fontFamily="Carlito, Calibri, Arial, sans-serif" fontWeight="bold" fontSize="72" fill="#004C97">OTE</text>
    <polyline points="120,72 130,50 145,90 160,30 175,72" fill="none" stroke="#00A9CE" strokeWidth="7" strokeLinejoin="round" />
  </svg>
)

const navItems = [
  { text: 'Úvod', icon: <HomeIcon />, section: 'intro' },
  { text: 'Odstávky', icon: <AssignmentIcon />, section: 'outages' },
  { text: 'Checklist', icon: <AssignmentIcon />, section: 'checklist' },
  { text: 'Historie', icon: <HistoryIcon />, section: 'history' },
  { text: 'Nastavení', icon: <SettingsIcon />, section: 'settings' },
]

function App() {
  const [drawerOpen, setDrawerOpen] = useState(false)
  const [section, setSection] = useState('intro')
  const [folder, setFolder] = useState(null)
  const [user, setUser] = useState('')
  const [outage, setOutage] = useState(null)
  const [checklistDone, setChecklistDone] = useState(false)
  // Snackbar
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' })
  // Tmavý/světlý režim
  const [mode, setMode] = useState('light')
  const theme = useMemo(() => createTheme({
    palette: {
      mode,
      primary: { main: '#004C97' },
      secondary: { main: '#00A9CE' },
      success: { main: '#64A70B' },
      error: { main: '#DF4661' },
      warning: { main: '#FFBF3F' },
      info: { main: '#B76CA4' },
      text: { primary: '#004C97', secondary: '#616265' },
      background: { default: mode === 'light' ? '#f7fafd' : '#1a2233' },
    },
    typography: {
      fontFamily: 'Carlito, Calibri, Arial, sans-serif',
      h1: { fontWeight: 700 },
      h2: { fontWeight: 700 },
      h3: { fontWeight: 700 },
      h4: { fontWeight: 700 },
      h5: { fontWeight: 700 },
      h6: { fontWeight: 700 },
      button: { textTransform: 'none', fontWeight: 600 },
    },
    shape: { borderRadius: 10 },
  }), [mode])

  const handleNav = (section) => {
    setSection(section)
    setDrawerOpen(false)
  }

  // Funkce pro volání notifikací
  const showSnackbar = (message, severity = 'success') => {
    setSnackbar({ open: true, message, severity })
  }

  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <SnackbarContext.Provider value={showSnackbar}>
        <Box sx={{ flexGrow: 1, minHeight: '100vh', background: theme.palette.background.default }}>
          <AppBar position="static" color="primary" elevation={2}>
            <Toolbar>
              <IconButton edge="start" color="inherit" aria-label="menu" onClick={() => setDrawerOpen(true)} sx={{ mr: 2 }}>
                <MenuIcon />
              </IconButton>
              <OTELogo />
              <Typography variant="h6" component="div" sx={{ flexGrow: 1, fontWeight: 700 }}>
                Evidence checklistů odstávek OTE
              </Typography>
              <IconButton sx={{ ml: 1 }} onClick={() => setMode(mode === 'light' ? 'dark' : 'light')} color="inherit">
                {mode === 'dark' ? <Brightness7Icon /> : <Brightness4Icon />}
              </IconButton>
            </Toolbar>
          </AppBar>
          <Drawer anchor="left" open={drawerOpen} onClose={() => setDrawerOpen(false)}>
            <Box sx={{ width: 250 }} role="presentation">
              <List>
                {navItems.map((item) => (
                  <ListItem key={item.section} disablePadding>
                    <ListItemButton selected={section === item.section} onClick={() => handleNav(item.section)}>
                      <ListItemIcon>{item.icon}</ListItemIcon>
                      <ListItemText primary={item.text} />
                    </ListItemButton>
                  </ListItem>
                ))}
              </List>
              <Divider />
            </Box>
          </Drawer>
          <Box sx={{
            p: 3,
            maxWidth: 1600,
            margin: '0 auto',
            width: '100%',
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'stretch',
            minHeight: 'calc(100vh - 64px)',
          }}>
            {section === 'intro' && (
              <WelcomeCard 
                folder={folder}
                setFolder={setFolder}
                user={user}
                setUser={setUser}
              />
            )}
            {section === 'outages' && (
              <>
                <Typography variant="h5" sx={{ mb: 2, color: 'primary.main', fontWeight: 700 }}>Odstávky</Typography>
                <OutageList onOutageSelected={setOutage} />
                {outage && (
                  <Box sx={{ mt: 2 }}>
                    <Typography variant="subtitle1" color="secondary.main">Vybraná odstávka: <b>{outage.date} {outage.from}-{outage.to} ({outage.note})</b></Typography>
                  </Box>
                )}
              </>
            )}
            {section === 'checklist' && (
              <>
                <Typography variant="h5" sx={{ mb: 2, color: 'primary.main', fontWeight: 700 }}>Checklist</Typography>
                {folder && user && outage && !checklistDone ? (
                  <Checklist onComplete={() => { setChecklistDone(true); showSnackbar('Checklist úspěšně dokončen!', 'success') }} />
                ) : !folder || !user || !outage ? (
                  <Typography color="error.main">Nejprve vyberte složku, zadejte jméno a vyberte odstávku v příslušných sekcích.</Typography>
                ) : (
                  <Typography color="success.main">Checklist hotov ✔️</Typography>
                )}
              </>
            )}
            {section === 'history' && (
              <>
                <Typography variant="h5" sx={{ mb: 2, color: 'primary.main', fontWeight: 700 }}>Historie kontrol</Typography>
                <Dashboard />
              </>
            )}
            {section === 'settings' && (
              <>
                <Typography variant="h5" sx={{ mb: 2, color: 'primary.main', fontWeight: 700 }}>Nastavení</Typography>
                <Typography variant="body1">Zde budou možnosti nastavení (změna složky, vzhledu, import/export, logo atd.).</Typography>
              </>
            )}
          </Box>
          <Snackbar open={snackbar.open} autoHideDuration={4000} onClose={() => setSnackbar({ ...snackbar, open: false })} anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}>
            <MuiAlert elevation={6} variant="filled" onClose={() => setSnackbar({ ...snackbar, open: false })} severity={snackbar.severity} sx={{ width: '100%' }}>
              {snackbar.message}
            </MuiAlert>
          </Snackbar>
        </Box>
      </SnackbarContext.Provider>
    </ThemeProvider>
  )
}

export default App
