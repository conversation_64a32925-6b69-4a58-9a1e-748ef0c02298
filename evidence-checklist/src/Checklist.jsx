import React, { useState } from 'react';
import { Card, CardContent, Typography, Checkbox, Button, Box, LinearProgress, Snackbar, Alert, Dialog, DialogTitle, DialogContent, DialogActions, TextField, Tooltip, IconButton } from '@mui/material';
import AssignmentTurnedInIcon from '@mui/icons-material/AssignmentTurnedIn';
import InfoIcon from '@mui/icons-material/Info';
import checklistData from './checklistData';

function Checklist({ onComplete }) {
  const [checked, setChecked] = useState([]);
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [note, setNote] = useState('');
  const [detailOpen, setDetailOpen] = useState(false);
  const [detailText, setDetailText] = useState('');
  const [exported, setExported] = useState(false);

  const handleCheck = (id) => {
    setChecked(prev => prev.includes(id) ? prev.filter(i => i !== id) : [...prev, id]);
  };

  const handleFinish = () => {
    if (checked.length === checklistData.length) {
      setDialogOpen(true);
    }
  };

  const handleDialogClose = () => {
    setDialogOpen(false);
    setSnackbarOpen(true);
    onComplete && onComplete({ checked, note });
    setNote('');
    setExported(false);
  };

  const handleDetailOpen = (detail) => {
    setDetailText(detail);
    setDetailOpen(true);
  };
  const handleDetailClose = () => {
    setDetailOpen(false);
    setDetailText('');
  };

  const handleExport = () => {
    const data = checklistData.map(item => ({
      id: item.id,
      title: item.title,
      checked: checked.includes(item.id),
      note: item.detail
    }));
    const result = {
      completedAt: new Date().toISOString(),
      note,
      checklist: data
    };
    const blob = new Blob([JSON.stringify(result, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `ote-checklist-${new Date().toISOString().slice(0, 10)}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    setExported(true);
  };

  const progress = (checked.length / checklistData.length) * 100;

  return (
    <Card sx={{ boxShadow: 6, mb: 3, borderRadius: 4, maxWidth: { xs: '100vw', md: 700 }, margin: '0 auto', width: '100%', p: 3, background: 'linear-gradient(135deg, #f7fafd 80%, #e3f6f9 100%)' }}>
      <CardContent>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
          <AssignmentTurnedInIcon color="primary" sx={{ fontSize: 36, mr: 2 }} />
          <Typography variant="h4" sx={{ color: 'primary.main', fontWeight: 800 }}>
            Checklist odstávky
          </Typography>
        </Box>
        <LinearProgress variant="determinate" value={progress} sx={{ height: 12, borderRadius: 6, mb: 4, background: '#e3f6f9' }} />
        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
          {checklistData.map((item) => {
            const isChecked = checked.includes(item.id);
            return (
              <Box
                key={item.id}
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  p: 2,
                  borderRadius: 3,
                  background: isChecked ? '#e0f7e9' : '#fff',
                  boxShadow: isChecked ? 2 : 0,
                  transition: 'all 0.2s',
                  gap: 2,
                  cursor: 'pointer',
                  '&:hover': { background: isChecked ? '#b2f2d7' : '#e3f6f9' },
                  '&:focus-visible': { outline: '2px solid #1976d2' },
                }}
                tabIndex={0}
                aria-checked={isChecked}
                role="checkbox"
                onClick={() => handleCheck(item.id)}
                onKeyDown={e => { if (e.key === ' ' || e.key === 'Enter') handleCheck(item.id); }}
              >
                <Checkbox
                  checked={isChecked}
                  onChange={() => handleCheck(item.id)}
                  color="success"
                  inputProps={{ 'aria-label': item.title }}
                  sx={{ mr: 2, '&:focus-visible': { outline: '2px solid #1976d2' } }}
                />
                <Typography
                  variant="h6"
                  sx={{
                    color: isChecked ? 'success.main' : 'text.primary',
                    fontWeight: 600,
                    fontSize: 20,
                    textDecoration: isChecked ? 'line-through' : 'none',
                    flexGrow: 1,
                  }}
                >
                  {item.title}
                </Typography>
                {item.detail && (
                  <Tooltip title="Zobrazit detail" arrow>
                    <IconButton
                      onClick={e => { e.stopPropagation(); handleDetailOpen(item.detail); }}
                      color="info"
                      size="large"
                      aria-label={`Zobrazit detail bodu: ${item.title}`}
                      sx={{ ml: 1, '&:focus-visible': { outline: '2px solid #1976d2' } }}
                    >
                      <InfoIcon sx={{ fontSize: 32 }} />
                    </IconButton>
                  </Tooltip>
                )}
              </Box>
            );
          })}
        </Box>
        <Button
          variant="contained"
          color="primary"
          size="large"
          onClick={handleFinish}
          sx={{ mt: 4, fontWeight: 700, minWidth: 200, fontSize: 18, py: 1.5, '&:focus-visible': { outline: '2px solid #1976d2' } }}
          disabled={checked.length !== checklistData.length}
          aria-label="Dokončit checklist"
        >
          Dokončit checklist
        </Button>
        <Snackbar open={snackbarOpen} autoHideDuration={3000} onClose={() => setSnackbarOpen(false)} anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}>
          <Alert onClose={() => setSnackbarOpen(false)} severity="success" sx={{ width: '100%' }}>
            Checklist úspěšně dokončen!
          </Alert>
        </Snackbar>
        <Dialog open={dialogOpen} onClose={handleDialogClose} maxWidth="sm" fullWidth>
          <DialogTitle>Poznámka k checklistu</DialogTitle>
          <DialogContent>
            <TextField
              label="Poznámka (volitelné)"
              value={note}
              onChange={e => setNote(e.target.value)}
              fullWidth
              multiline
              minRows={2}
              sx={{ mt: 2 }}
            />
          </DialogContent>
          <DialogActions>
            <Button onClick={handleDialogClose} color="primary" variant="contained" aria-label="Uložit a zavřít">Uložit a zavřít</Button>
            <Button onClick={handleExport} color="secondary" variant="outlined" aria-label="Exportovat výsledek" disabled={exported} sx={{ ml: 2 }}>
              {exported ? 'Exportováno' : 'Exportovat výsledek'}
            </Button>
          </DialogActions>
        </Dialog>
        <Dialog open={detailOpen} onClose={handleDetailClose} maxWidth="xs" fullWidth>
          <DialogTitle>Detail bodu</DialogTitle>
          <DialogContent>
            <Typography>{detailText}</Typography>
          </DialogContent>
          <DialogActions>
            <Button onClick={handleDetailClose} color="primary">Zavřít</Button>
          </DialogActions>
        </Dialog>
      </CardContent>
    </Card>
  );
}

export default Checklist; 