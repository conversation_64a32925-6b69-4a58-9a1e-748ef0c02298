import React, { useState } from 'react';
import { Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper, Button, TextField, Box, Typography, MenuItem, Grid, Snackbar, Alert } from '@mui/material';
import PlaylistAddIcon from '@mui/icons-material/PlaylistAdd';
import EventAvailableIcon from '@mui/icons-material/EventAvailable';

const initialOutages = [
  { id: 1, date: '21.01.2025', from: '17:00', to: '22:00', note: 'úterý' },
  { id: 2, date: '18.02.2025', from: '17:00', to: '22:00', note: 'úterý' },
  { id: 3, date: '18.03.2025', from: '16:00', to: '21:00', note: 'mi<PERSON><PERSON><PERSON><PERSON><PERSON>, úter<PERSON>' },
  { id: 4, date: '25.03.2025', from: '17:00', to: '22:00', note: 'úterý' },
  { id: 5, date: '22.04.2025', from: '17:00', to: '22:00', note: 'úterý' },
  { id: 6, date: '20.05.2025', from: '17:00', to: '22:00', note: 'úterý' },
  { id: 7, date: '24.06.2025', from: '17:00', to: '22:00', note: 'úterý' },
  { id: 8, date: '22.07.2025', from: '17:00', to: '22:00', note: 'úterý' },
  { id: 9, date: '26.08.2025', from: '17:00', to: '22:00', note: 'úterý' },
  { id: 10, date: '23.09.2025', from: '17:00', to: '22:00', note: 'úterý' },
  { id: 11, date: '21.10.2025', from: '17:00', to: '22:00', note: 'úterý' },
  { id: 12, date: '25.11.2025', from: '17:00', to: '22:00', note: 'úterý' },
  { id: 13, date: '16.12.2025', from: '17:00', to: '22:00', note: 'úterý' },
];

const templates = [
  { label: 'Pravidelná úterní odstávka', from: '17:00', to: '22:00', note: 'úterý' },
  { label: 'Mimořádná odstávka', from: '16:00', to: '21:00', note: 'mimořádná odstávka, úterý' },
];

function AddOutageForm({ onAdd, onCancel, template, setTemplate }) {
  const [newOutage, setNewOutage] = useState({ date: '', from: '', to: '', note: '' });
  const [error, setError] = useState('');

  const handleTemplate = (e) => {
    const t = templates.find(t => t.label === e.target.value);
    setTemplate(e.target.value);
    if (t) {
      setNewOutage({ ...newOutage, from: t.from, to: t.to, note: t.note });
    }
  };

  const validate = () => {
    if (!/^\d{2}\.\d{2}\.\d{4}$/.test(newOutage.date)) return 'Zadejte datum ve formátu DD.MM.RRRR';
    if (!/^\d{2}:\d{2}$/.test(newOutage.from)) return 'Zadejte čas od ve formátu HH:MM';
    if (!/^\d{2}:\d{2}$/.test(newOutage.to)) return 'Zadejte čas do ve formátu HH:MM';
    return '';
  };

  const handleAdd = (e) => {
    e.preventDefault();
    const err = validate();
    if (err) {
      setError(err);
      return;
    }
    onAdd(newOutage);
    setNewOutage({ date: '', from: '', to: '', note: '' });
    setTemplate('');
    setError('');
  };

  return (
    <Paper elevation={6} sx={{ p: { xs: 2, md: 4 }, mb: 4, maxWidth: { xs: '100vw', md: 1200, lg: 1440 }, mx: 'auto', background: '#f7fafd', borderRadius: 4, boxShadow: 6 }}>
      <Typography variant="h5" sx={{ mb: 2, color: 'primary.main', fontWeight: 700 }}>Nová odstávka</Typography>
      <form onSubmit={handleAdd} aria-label="Formulář pro přidání odstávky">
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} md={2}>
            <TextField label="Datum" type="text" value={newOutage.date} onChange={e => setNewOutage({ ...newOutage, date: e.target.value })} required size="medium" fullWidth inputProps={{ 'aria-label': 'Datum odstávky' }} />
          </Grid>
          <Grid item xs={12} md={2}>
            <TextField label="Čas od" type="text" value={newOutage.from} onChange={e => setNewOutage({ ...newOutage, from: e.target.value })} required size="medium" fullWidth inputProps={{ 'aria-label': 'Čas od' }} />
          </Grid>
          <Grid item xs={12} md={2}>
            <TextField label="Čas do" type="text" value={newOutage.to} onChange={e => setNewOutage({ ...newOutage, to: e.target.value })} required size="medium" fullWidth inputProps={{ 'aria-label': 'Čas do' }} />
          </Grid>
          <Grid item xs={12} md={3}>
            <TextField label="Poznámka" type="text" value={newOutage.note} onChange={e => setNewOutage({ ...newOutage, note: e.target.value })} size="medium" fullWidth inputProps={{ 'aria-label': 'Poznámka' }} />
          </Grid>
          <Grid item xs={12} md={2}>
            <TextField select label="Šablona" value={template} onChange={handleTemplate} size="medium" fullWidth inputProps={{ 'aria-label': 'Šablona odstávky' }}>
              <MenuItem value="">Žádná šablona</MenuItem>
              {templates.map(t => <MenuItem key={t.label} value={t.label}>{t.label}</MenuItem>)}
            </TextField>
          </Grid>
          <Grid item xs={12} md={1} sx={{ display: 'flex', gap: 1 }}>
            <Button type="submit" variant="contained" color="primary" startIcon={<PlaylistAddIcon />} sx={{ fontWeight: 600, minWidth: 120, fontSize: 16, borderRadius: 3 }} aria-label="Přidat odstávku">
              Přidat
            </Button>
            <Button type="button" variant="outlined" color="secondary" onClick={onCancel} sx={{ minWidth: 100, fontSize: 16, borderRadius: 3 }} aria-label="Zrušit přidání odstávky">
              Zrušit
            </Button>
          </Grid>
        </Grid>
        {error && <Alert severity="error" sx={{ mt: 2 }}>{error}</Alert>}
      </form>
    </Paper>
  );
}

function OutageList({ onOutageSelected }) {
  const [outages, setOutages] = useState(initialOutages);
  const [showForm, setShowForm] = useState(false);
  const [template, setTemplate] = useState('');
  const [selectedId, setSelectedId] = useState(null);
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' });

  const handleSelect = (id) => {
    setSelectedId(id);
    const outage = outages.find(o => o.id === id);
    onOutageSelected(outage);
  };

  const handleAdd = (newOutage) => {
    const id = outages.length ? Math.max(...outages.map(o => o.id)) + 1 : 1;
    const updated = [...outages, { ...newOutage, id }];
    setOutages(updated);
    setShowForm(false);
    setTemplate('');
    setSelectedId(id); // automaticky vybrat novou odstávku
    setSnackbar({ open: true, message: 'Odstávka úspěšně přidána.', severity: 'success' });
    onOutageSelected({ ...newOutage, id });
  };

  return (
    <Box sx={{ width: '100%', maxWidth: { xs: '100vw', md: 1200, lg: 1440 }, mx: 'auto' }}>
      <Typography variant="h4" sx={{ mb: 3, color: 'primary.main', fontWeight: 800, textAlign: 'left', fontSize: 32 }}>
        Seznam odstávek
      </Typography>
      <TableContainer component={Paper} sx={{ mb: 3, borderRadius: 4, width: '100%', maxWidth: { xs: '100vw', md: 1200, lg: 1440 }, mx: 'auto', boxShadow: 6 }} aria-label="Tabulka odstávek" role="table">
        <Table size="medium">
          <TableHead>
            <TableRow sx={{ background: '#e3f6f9' }}>
              <TableCell sx={{ fontWeight: 700, fontSize: 20 }}>Datum</TableCell>
              <TableCell sx={{ fontWeight: 700, fontSize: 20 }}>Čas od</TableCell>
              <TableCell sx={{ fontWeight: 700, fontSize: 20 }}>Čas do</TableCell>
              <TableCell sx={{ fontWeight: 700, fontSize: 20 }}>Poznámka</TableCell>
              <TableCell sx={{ width: 180 }}></TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {outages.map(o => (
              <TableRow key={o.id}
                hover
                selected={selectedId === o.id}
                sx={{
                  background: selectedId === o.id ? '#e3f2fd' : '#fff',
                  border: selectedId === o.id ? '2px solid #1976d2' : '2px solid transparent',
                  transition: 'background 0.2s, border 0.2s',
                  fontSize: 18,
                  '&:hover': { background: '#e3f6f9' },
                  cursor: 'pointer',
                }}
                aria-selected={selectedId === o.id}
                role="row"
                onClick={() => handleSelect(o.id)}
              >
                <TableCell sx={{ fontSize: 18 }}>{o.date}</TableCell>
                <TableCell sx={{ fontSize: 18 }}>{o.from}</TableCell>
                <TableCell sx={{ fontSize: 18 }}>{o.to}</TableCell>
                <TableCell sx={{ fontSize: 18 }}>{o.note}</TableCell>
                <TableCell align="right">
                  <Button 
                    variant={selectedId === o.id ? 'contained' : 'outlined'} 
                    color="secondary" 
                    size="large" 
                    startIcon={<EventAvailableIcon />} 
                    onClick={e => { e.stopPropagation(); handleSelect(o.id); }}
                    sx={{ fontWeight: 700, minWidth: 120, fontSize: 17, borderRadius: 3 }}
                    aria-label={`Vybrat odstávku ${o.date}`}
                  >
                    Vybrat
                  </Button>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
      <Box sx={{ display: 'flex', justifyContent: 'flex-end', mb: 2 }}>
        <Button variant="contained" color="primary" onClick={() => setShowForm(true)} startIcon={<PlaylistAddIcon />} sx={{ fontWeight: 800, minWidth: 240, fontSize: 20, borderRadius: 3, boxShadow: 4 }} aria-label="Přidat novou odstávku">
          Přidat novou odstávku
        </Button>
      </Box>
      {showForm && (
        <AddOutageForm onAdd={handleAdd} onCancel={() => setShowForm(false)} template={template} setTemplate={setTemplate} />
      )}
      <Snackbar open={snackbar.open} autoHideDuration={3000} onClose={() => setSnackbar({ ...snackbar, open: false })} anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}>
        <Alert onClose={() => setSnackbar({ ...snackbar, open: false })} severity={snackbar.severity} sx={{ width: '100%' }}>
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
}

export default OutageList; 