import React from 'react'
import { Box } from '@mui/material'
import { useApp } from '../contexts/AppContext'
import AppHeader from './AppHeader'
import AppSidebar from './AppSidebar'
import MainContent from './MainContent'

function AppLayout() {
  const { state } = useApp()

  return (
    <Box sx={{ 
      display: 'flex', 
      minHeight: '100vh',
      backgroundColor: 'background.default'
    }}>
      {/* Hlavička */}
      <AppHeader />
      
      {/* Sidebar */}
      <AppSidebar />
      
      {/* Hlavní obsah */}
      <Box
        component="main"
        sx={{
          flexGrow: 1,
          display: 'flex',
          flexDirection: 'column',
          minHeight: '100vh',
          pt: '64px', // Výška AppBar
        }}
      >
        <MainContent />
      </Box>
    </Box>
  )
}

export default AppLayout
